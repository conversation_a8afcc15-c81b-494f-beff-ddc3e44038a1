import os
import sys
import json
import time
import socket
import threading
import logging
from datetime import datetime
from pathlib import Path

import cv2
import numpy as np
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget, QTextEdit, QScrollArea

from ultralytics import YOL<PERSON>

try:
    from io_trigger import trigger_port1_low_3s
except ImportError:
    def trigger_port1_low_3s(blocking=False):
        print("[MOCK] IO 报警触发！")

# ---------- 日志 ----------
LOG_DIR = Path("test_log")
LOG_DIR.mkdir(exist_ok=True)
log_file = LOG_DIR / f"{datetime.now().strftime('%Y%m%d')}.log"

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding="utf-8"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("YOLO_PLC")

# ---------- 热加载 ----------
CONFIG_PATH = Path("config/config.json")

def load_config():
    with CONFIG_PATH.open(encoding="utf-8") as f:
        cfg = json.load(f)
    cfg["H_MATRIX"] = np.array(cfg["H_MATRIX"], dtype=float)
    cfg["OFFSETS"] = [list(map(float, off)) for off in cfg["OFFSETS"]]
    cfg["ROI_START"] = tuple(map(int, cfg["ROI_START"]))
    cfg["ROI_END"] = tuple(map(int, cfg["ROI_END"]))
    return cfg

# ---------- 工具 ----------
def pixel_to_robot(pixel_points, H):
    robot_points = []
    for x, y in pixel_points:
        p = np.array([x, y, 1], dtype=float).reshape(3, 1)
        p_robot = H @ p
        p_robot = (p_robot / p_robot[2])[:2].flatten()
        robot_points.append(p_robot)
    return robot_points

# ---------- YOLO ----------
class YoloWrapper:
    def __init__(self, model_path):
        self.model = YOLO(model_path)

    def detect_top_one(self, img_bgr: np.ndarray):
        cfg = load_config()
        h, w = img_bgr.shape[:2]
        x1_roi = max(0, min(cfg["ROI_START"][0], w - 1))
        y1_roi = max(0, min(cfg["ROI_START"][1], h - 1))
        x2_roi = max(x1_roi + 1, min(cfg["ROI_END"][0], w - 1))
        y2_roi = max(y1_roi + 1, min(cfg["ROI_END"][1], h - 1))
        roi_img = img_bgr[y1_roi:y2_roi, x1_roi:x2_roi]

        results = self.model.predict(roi_img, verbose=False, imgsz=640, conf=cfg["CONF"])
        if not results or len(results[0].boxes) == 0:
            return None, None, None

        boxes = results[0].boxes.xyxy.cpu().numpy()
        confs = results[0].boxes.conf.cpu().numpy()
        top_idx = np.argmin(boxes[:, 1])
        rx1, ry1, rx2, ry2 = boxes[top_idx]
        conf = round(float(confs[top_idx]), 3)

        pixel_coords = [
            int(rx1 + x1_roi), int(ry1 + y1_roi),
            int(rx1 + x1_roi), int(ry2 + y1_roi),
            int(rx2 + x1_roi), int(ry2 + y1_roi),
            int(rx2 + x1_roi), int(ry1 + y1_roi)
        ]

        robot_points = pixel_to_robot(
            [[pixel_coords[i], pixel_coords[i + 1]] for i in range(0, 8, 2)],
            cfg["H_MATRIX"]
        )
        robot_points_offset = [
            [robot_points[i][0] + cfg["OFFSETS"][i][0],
             robot_points[i][1] + cfg["OFFSETS"][i][1]]
            for i in range(4)
        ]
        robot_coords = [round(v, 2) for pt in robot_points_offset for v in pt]
        return pixel_coords, robot_coords, conf

# ---------- Socket ----------
class PlcCommThread(QThread):
    new_frame_signal = pyqtSignal(np.ndarray, list, float)
    log_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = True

    def run(self):
        while self.running:
            cfg = load_config()
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server:
                    server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    server.bind((cfg["HOST"], cfg["PORT"]))
                    server.listen(1)
                    logger.info(f"服务器已启动 {cfg['HOST']}:{cfg['PORT']}")
                    while self.running:
                        conn, addr = server.accept()
                        logger.info(f"PLC 已连接 {addr}")
                        self.handle_plc(conn, cfg)
            except Exception as e:
                logger.error(f"Socket 异常: {e}")
                trigger_port1_low_3s(blocking=False)
                time.sleep(2)

    def handle_plc(self, conn, cfg):
        try:
            while self.running:
                data = conn.recv(1024)
                if not data:
                    logger.info("PLC 断开连接")
                    break
                msg = int.from_bytes(data, byteorder='big', signed=False)  # 将接收到的字节转换为整数
                logger.info(f"<<< 收到 PLC 指令: {msg}")

                img_bgr = None  # 初始化 img_bgr 为 None
                pixel_coords = None
                conf = None

                if msg == 1:
                    # 处理指令 1（原来的逻辑）
                    img_path = Path(cfg["IMG_DIR"]).glob("*")
                    img_path = max(img_path, key=lambda p: p.stat().st_mtime) if img_path else None
                    if img_path is None:
                        response = "  (9)"
                        conn.sendall(response.encode())
                        logger.info(">>> 未找到图片")
                        continue

                    img_bgr = cv2.imdecode(np.fromfile(str(img_path), dtype=np.uint8), cv2.IMREAD_COLOR)
                    if img_bgr is None:
                        logger.error("cv2 读图失败")
                        response = "  (9)"
                        conn.sendall(response.encode())
                        logger.info(f">>> 发送异常响应 :{response}")
                        continue

                    try:
                        pixel_coords, robot_coords, conf = YoloWrapper(cfg["MODEL_PATH"]).detect_top_one(img_bgr)
                    except Exception as e:
                        logger.error(f"检测失败: {e}")
                        response = "  (9)"
                        conn.sendall(response.encode())
                        logger.info(f">>> 发送异常响应 :{response}")
                        continue

                    if robot_coords is None:
                        response = "  (0)"
                        conn.sendall(response.encode())
                        logger.info(">>> 无目标")
                    else:
                        response = f"  (1,{','.join(map(str, robot_coords))})"
                        conn.sendall(response.encode())
                        logger.info(f">>> 发送给 PLC 的数据: {response}")
                        logger.info(f"[检测到目标] 目标置信度={conf}")
                        logger.info(f"[像素坐标]  {','.join(map(str, pixel_coords))}")
                        logger.info(f"[机械臂坐标] {','.join(f'{v:.2f}' for v in robot_coords)}")

                elif msg == 2:
                    # 处理指令 2（输出异常信息）
                    logger.warning(">>> 收到指令 2，输出异常信息")
                    response = "  (2)"
                    conn.sendall(response.encode())
                    logger.info(f">>> 发送异常响应 :{response}")

                elif msg == 3:
                    # 处理指令 3（输出异常信息）
                    logger.warning(">>> 收到指令 3，输出异常信息")
                    response = "  (3)"
                    conn.sendall(response.encode())
                    logger.info(f">>> 发送异常响应 :{response}")

                else:
                    # 处理其他指令（输出未知指令信息）
                    logger.warning(f">>> 收到未知指令: {msg}")
                    response = "  (9)"
                    conn.sendall(response.encode())
                    logger.info(f">>> 发送异常响应 :{response}")

                # 接收 PLC 的确认响应
                ack_data = conn.recv(1024)
                if ack_data:
                    ack = int.from_bytes(ack_data, byteorder='big', signed=False)
                    if ack == 11:
                        logger.info(f"<<< PLC 已确认 : {ack}")
                    else:
                        logger.warning(f"<<< PLC 回复未知确认码: {ack}")
                else:
                    logger.warning("<<< PLC 未确认")

                if img_bgr is not None:
                    self.new_frame_signal.emit(img_bgr, pixel_coords or [], conf or 0.0)
                else:
                    logger.warning("未加载图像，跳过 GUI 更新")
        except Exception as e:
            logger.error(f"PLC 通信异常: {e}")
            response = "  (9)"
            conn.sendall(response.encode())
            logger.info(f">>> 发送异常响应 :{response}")
            trigger_port1_low_3s(blocking=False)

# 自定义日志处理器，将日志输出到 QTextEdit
class QTextEditLogger(logging.Handler):
    def __init__(self, text_edit):
        super().__init__()
        self.text_edit = text_edit

    def emit(self, record):
        msg = self.format(record)
        self.text_edit.append(msg)
        self.text_edit.verticalScrollBar().setValue(self.text_edit.verticalScrollBar().maximum())

# ---------- GUI ----------
class MainWindow(QMainWindow):
    def __init__(self, comm_thread: PlcCommThread):
        super().__init__()
        self.setWindowTitle("YOLO11 PLC 检测器")
        self.resize(1000, 800)  # 可以动态调整

        self.comm_thread = comm_thread
        self.comm_thread.new_frame_signal.connect(self.update_image)
        self.comm_thread.log_signal.connect(self.append_log)

        # 图像显示区域
        self.label_img = QLabel("等待图像...")
        self.label_img.setAlignment(Qt.AlignCenter)
        self.label_img.setStyleSheet("background-color:black;color:white;")
        self.label_img.setMinimumSize(640, 480)
        self.label_img.setScaledContents(True)  # 保持比例

        # 使用 QScrollArea 包裹 QLabel，允许滚动和自适应
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.label_img)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        self.scroll_area.setWidgetResizable(True)  # 自适应 QLabel 大小

        # 按钮
        self.btn_manual = QPushButton("手动触发一次检测")
        self.btn_manual.clicked.connect(self.manual_trigger)
        self.btn_save = QPushButton("保存当前图像")
        self.btn_save.clicked.connect(self.save_image)

        # 日志区域
        self.text_log = QTextEdit()
        self.text_log.setMaximumHeight(200)  # 增大日志区域高度
        self.text_log.setReadOnly(True)  # 设置为只读

        # 布局
        layout = QVBoxLayout()
        layout.addWidget(self.scroll_area)  # 添加滚动区域
        layout.addWidget(self.btn_manual)
        layout.addWidget(self.btn_save)
        layout.addWidget(QLabel("日志:"))
        layout.addWidget(self.text_log)

        central = QWidget()
        central.setLayout(layout)
        self.setCentralWidget(central)

        # 将日志记录器的输出重定向到 UI 界面
        self.log_handler = QTextEditLogger(self.text_log)
        logger.addHandler(self.log_handler)

    def manual_trigger(self):
        logger.info("[GUI] 手动触发检测...")
        cfg = load_config()
        img_path = Path(cfg["IMG_DIR"]).glob("*")
        img_path = max(img_path, key=lambda p: p.stat().st_mtime) if img_path else None
        if img_path is None:
            logger.info("未找到图片")
            return

        img_bgr = cv2.imdecode(np.fromfile(str(img_path), dtype=np.uint8), cv2.IMREAD_COLOR)
        if img_bgr is None:
            logger.info("cv2 读图失败")
            return

        pixel_coords, robot_coords, conf = YoloWrapper(cfg["MODEL_PATH"]).detect_top_one(img_bgr)
        if robot_coords is None:
            logger.info("无目标")
        else:
            logger.info(f"检测到目标,置信度={conf}")
            logger.info(f"[像素坐标]  {','.join(map(str, pixel_coords))}")
            logger.info(f"[机械臂坐标] {','.join(f'{v:.2f}' for v in robot_coords)}")

        self.update_image(img_bgr, pixel_coords or [], conf or 0.0)

    def save_image(self):
        if self.label_img.pixmap() is None:
            logger.info("没有可保存的图像")
            return

        save_dir = Path("test_save")
        save_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = save_dir / f"image_{timestamp}.jpg"

        pixmap = self.label_img.pixmap()
        pixmap.save(str(save_path))
        logger.info(f"图像已保存到 {save_path}")

    def update_image(self, img_bgr, pixel_coords, conf=None):
        if img_bgr is None:
            return

        cfg = load_config()
        h, w, ch = img_bgr.shape
        label_w = self.label_img.width()
        label_h = self.label_img.height()
        scale = min(label_w / w, label_h / h)
        new_w, new_h = int(w * scale), int(h * scale)

        img_show = cv2.resize(img_bgr, (new_w, new_h), interpolation=cv2.INTER_AREA)

        # 创建一个黑色背景图像
        background = np.zeros((label_h, label_w, ch), dtype=np.uint8)

        # 计算图像在背景中的位置
        start_x = (label_w - new_w) // 2
        start_y = (label_h - new_h) // 2

        # 将调整大小后的图像绘制到背景图像的中心
        background[start_y:start_y + new_h, start_x:start_x + new_w] = img_show

        def map_pt(x, y):
            return int((x * scale) + start_x), int((y * scale) + start_y)

        # ROI 红框
        cv2.rectangle(background,
                      map_pt(cfg["ROI_START"][0], cfg["ROI_START"][1]),
                      map_pt(cfg["ROI_END"][0], cfg["ROI_END"][1]),
                      (0, 0, 255), 2)

        # 检测框绿框
        if pixel_coords and conf is not None:
            pts = [(pixel_coords[i], pixel_coords[i + 1]) for i in range(0, 8, 2)]
            pts_scaled = [map_pt(x, y) for x, y in pts]
            for i in range(4):
                cv2.line(background, pts_scaled[i], pts_scaled[(i + 1) % 4], (0, 255, 0), 2)

            # 获取左上角
            x0, y0 = pts_scaled[0]

            # 文字内容
            text = f"Conf:{conf:.3f}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.3  # 字体大小
            thickness = 1  # 字体粗细
            (tw, th), _ = cv2.getTextSize(text, font, font_scale, thickness)

            # 文字位置
            text_org = (x0, y0 - 5)  # 紧贴检测框左上角，稍微往上一点

            # 文字颜色与检测框一致（绿色）
            text_color = (0, 255, 0)

            # 文字
            cv2.putText(background, text, text_org, font, font_scale, text_color, thickness, cv2.LINE_AA)

        # 将背景图像转换为 QImage 并显示
        qt_img = QImage(background.data, label_w, label_h, label_w * ch, QImage.Format_BGR888)
        self.label_img.setPixmap(QPixmap.fromImage(qt_img))

    def append_log(self, txt):
        self.text_log.append(txt)
        self.text_log.verticalScrollBar().setValue(self.text_log.verticalScrollBar().maximum())

# ---------- 主程序 ----------
def main():
    app = QApplication(sys.argv)
    comm = PlcCommThread()
    comm.start()
    win = MainWindow(comm)
    win.show()
    ret = app.exec_()
    comm.stop()
    comm.wait()
    sys.exit(ret)

if __name__ == "__main__":
    main()